# 优惠码功能说明文档

## 功能概述

本功能在现有优惠券系统基础上，新增了直接使用优惠码的功能。用户可以通过输入优惠码直接享受优惠，无需预先领取优惠券。

## 核心特性

### 1. 双模式支持
- **传统模式**：用户先领取优惠券，再在订单中使用
- **优惠码模式**：用户直接输入优惠码使用，无需预先领取

### 2. 业务类型区分
- **商品订单**：适用于普通商品购买订单
- **代购订单**：适用于代购服务订单

### 3. 优惠码类型
- **固定码**：管理员设置固定的优惠码，如 `WELCOME2024`
- **动态码**：系统自动生成的唯一优惠码，格式：`DC{模板ID}{时间戳后6位}{6位随机字符}`

### 4. 使用限制
- **总使用次数限制**：可设置优惠码的最大使用次数
- **用户使用次数限制**：可设置每个用户的使用次数上限
- **有效期限制**：支持固定日期和相对天数两种方式
- **订单金额限制**：可设置最低消费金额
- **商品范围限制**：可限制适用的商品范围

## 数据库设计

### 1. 优惠券模板表扩展 (promotion_coupon_template)

新增字段：
```sql
ALTER TABLE promotion_coupon_template ADD COLUMN coupon_code VARCHAR(50) NULL COMMENT '优惠码（为空表示非优惠码模式）';
ALTER TABLE promotion_coupon_template ADD COLUMN code_type TINYINT DEFAULT 1 COMMENT '优惠码类型：1-固定码，2-动态码';
ALTER TABLE promotion_coupon_template ADD COLUMN usage_type TINYINT DEFAULT 1 COMMENT '使用方式：1-领取后使用，2-直接使用优惠码';
ALTER TABLE promotion_coupon_template ADD COLUMN business_type TINYINT DEFAULT 1 COMMENT '业务类型：1-商品订单，2-代购订单';
ALTER TABLE promotion_coupon_template ADD COLUMN max_use_count INT DEFAULT -1 COMMENT '优惠码最大使用次数（-1表示无限制）';
ALTER TABLE promotion_coupon_template ADD COLUMN used_count INT DEFAULT 0 COMMENT '优惠码已使用次数';

CREATE INDEX idx_coupon_code ON promotion_coupon_template(coupon_code);
CREATE INDEX idx_business_type ON promotion_coupon_template(business_type);
```

### 2. 优惠码使用记录表 (promotion_coupon_usage_log)

```sql
CREATE TABLE promotion_coupon_usage_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_id BIGINT NOT NULL COMMENT '优惠券模板ID',
    coupon_code VARCHAR(50) NOT NULL COMMENT '使用的优惠码',
    user_id BIGINT NOT NULL COMMENT '使用用户ID',
    business_type TINYINT NOT NULL COMMENT '业务类型：1-商品订单，2-代购订单',
    business_id BIGINT NOT NULL COMMENT '业务ID（订单ID或代购包裹ID）',
    discount_amount INT NOT NULL COMMENT '优惠金额（分）',
    use_time DATETIME NOT NULL COMMENT '使用时间',
    creator VARCHAR(64) DEFAULT '',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(64) DEFAULT '',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BIT NOT NULL DEFAULT FALSE,
    tenant_id BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_template_id (template_id),
    INDEX idx_coupon_code (coupon_code),
    INDEX idx_user_id (user_id),
    INDEX idx_business_id (business_id),
    INDEX idx_business_type (business_type)
) COMMENT '优惠码使用记录表';
```

### 3. 订单表扩展

商品订单表 (trade_order) 和代购订单表 (agent_parcel) 都新增：
```sql
ALTER TABLE trade_order ADD COLUMN coupon_code VARCHAR(50) NULL COMMENT '优惠码';
ALTER TABLE agent_parcel ADD COLUMN coupon_code VARCHAR(50) NULL COMMENT '优惠码';
```

## 核心类说明

### 1. 枚举类

#### CouponCodeTypeEnum - 优惠码类型
```java
FIXED(1, "固定码"),     // 如：WELCOME2024
DYNAMIC(2, "动态码");   // 如：根据规则生成的唯一码
```

#### CouponUsageTypeEnum - 使用方式
```java
TAKE_FIRST(1, "领取后使用"),
DIRECT_USE(2, "直接使用优惠码");
```

#### CouponBusinessTypeEnum - 业务类型
```java
TRADE_ORDER(1, "商品订单"),
AGENT_ORDER(2, "代购订单");
```

### 2. 核心服务类

#### CouponCodeService - 优惠码服务
- `validateCouponCode()` - 验证优惠码是否可用
- `useCouponCode()` - 使用优惠码
- `generateDynamicCouponCode()` - 生成动态优惠码

#### CouponUsageLogMapper - 使用记录Mapper
- `countByCouponCode()` - 统计优惠码使用次数
- `countByCouponCodeAndUserId()` - 统计用户使用次数

### 3. 价格计算器增强

#### TradeCouponPriceCalculator - 商品订单优惠券计算器
- 新增 `handleCouponCode()` 方法处理优惠码逻辑
- 支持优惠码验证和价格计算

#### AgentCouponPriceCalculator - 代购订单优惠券计算器
- 新增 `handleCouponCode()` 方法处理优惠码逻辑
- 支持代购订单的优惠码使用

## API接口

### 1. 前端用户接口

#### 验证优惠码
```
POST /app-api/promotion/coupon/validate-code
```

请求参数：
```json
{
  "couponCode": "WELCOME2024",
  "businessType": 1,
  "totalAmount": 10000
}
```

响应参数：
```json
{
  "valid": true,
  "couponName": "新用户专享优惠券",
  "discountAmount": 1000,
  "discountType": 1,
  "discountPrice": 10
}
```

#### 订单结算（支持优惠码）
商品订单结算和代购订单结算的请求VO都新增了 `couponCode` 字段：
```json
{
  "items": [...],
  "couponCode": "WELCOME2024"
}
```

### 2. 管理后台接口

优惠券模板的创建和编辑接口支持新增的优惠码相关字段：
- `couponCode` - 优惠码
- `codeType` - 优惠码类型
- `usageType` - 使用方式
- `businessType` - 业务类型
- `maxUseCount` - 最大使用次数

## 使用场景示例

### 1. 新用户注册码
```
优惠码：WELCOME2024
类型：固定码
业务类型：商品订单
使用方式：直接使用优惠码
用户限制：每人限用1次
优惠内容：满100元减20元
```

### 2. 节日促销码
```
优惠码：SPRING2024
类型：固定码
业务类型：全部
使用方式：直接使用优惠码
总次数限制：1000次
优惠内容：8折优惠，最高优惠50元
```

### 3. 邮件专属码
```
优惠码：DC123456789012ABCDEF（动态生成）
类型：动态码
业务类型：代购订单
使用方式：直接使用优惠码
使用限制：仅限1次
优惠内容：固定减免30元
```

## 动态码生成规则

动态优惠码格式：`DC{模板ID}{时间戳后6位}{6位随机字符}`

示例：
- 模板ID：123
- 时间戳：1703123456789（取后6位：456789）
- 随机字符：ABCDEF
- 最终优惠码：DC123456789ABCDEF

## 验证逻辑

优惠码验证按以下顺序进行：
1. 优惠码是否存在
2. 优惠券模板状态是否启用
3. 使用方式是否为"直接使用优惠码"
4. 业务类型是否匹配
5. 有效期是否在范围内
6. 总使用次数是否超限
7. 用户使用次数是否超限
8. 订单金额是否满足条件
9. 商品范围是否匹配
10. 计算优惠金额

## 注意事项

1. **向后兼容**：现有的优惠券领取-使用模式完全保留，不影响现有功能
2. **互斥使用**：一个订单只能使用优惠券或优惠码中的一种，不能同时使用
3. **业务隔离**：商品订单和代购订单的优惠码相互独立
4. **并发安全**：使用数据库乐观锁防止优惠码被重复使用
5. **日志记录**：所有优惠码使用都会记录到使用日志表中

## 管理后台功能

1. **优惠券模板管理**：支持创建和编辑优惠码模式的优惠券模板
2. **使用统计**：查看优惠码的使用次数和使用详情
3. **用户使用记录**：查看特定用户的优惠码使用历史
4. **动态码生成**：为特定模板生成动态优惠码

## 扩展性

该设计具有良好的扩展性，可以轻松支持：
1. 更多业务类型（如积分兑换、会员专享等）
2. 更复杂的优惠码生成规则
3. 批量优惠码生成和管理
4. 优惠码的导入导出功能

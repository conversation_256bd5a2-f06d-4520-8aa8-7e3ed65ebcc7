package cn.iocoder.yudao.module.promotion.controller.app.coupon.vo.code;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 App - 优惠码验证 Response VO")
@Data
public class AppCouponCodeValidateRespVO {

    @Schema(description = "是否有效", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean valid;

    @Schema(description = "无效原因", example = "优惠码已过期")
    private String invalidReason;

    @Schema(description = "优惠券名称", example = "新用户专享优惠券")
    private String couponName;

    @Schema(description = "优惠金额（分）", example = "1000")
    private Integer discountAmount;

    @Schema(description = "优惠类型", example = "1")
    private Integer discountType;

    @Schema(description = "折扣百分比", example = "80")
    private Integer discountPercent;

    @Schema(description = "优惠金额", example = "10")
    private Integer discountPrice;

}

package cn.iocoder.yudao.module.promotion.service.coupon;

import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeUseReqBO;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeValidateReqBO;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeValidateRespBO;

/**
 * 优惠码 Service 接口
 *
 * <AUTHOR>
 */
public interface CouponCodeService {

    /**
     * 验证优惠码是否可用
     *
     * @param reqBO 验证请求
     * @return 验证结果
     */
    CouponCodeValidateRespBO validateCouponCode(CouponCodeValidateReqBO reqBO);

    /**
     * 使用优惠码
     *
     * @param reqBO 使用请求
     */
    void useCouponCode(CouponCodeUseReqBO reqBO);

    /**
     * 生成动态优惠码
     *
     * @param templateId 模板ID
     * @return 优惠码
     */
    String generateDynamicCouponCode(Long templateId);

}

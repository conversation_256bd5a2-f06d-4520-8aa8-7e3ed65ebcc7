package cn.iocoder.yudao.module.promotion.controller.app.coupon.vo.code;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "用户 App - 优惠码验证 Request VO")
@Data
public class AppCouponCodeValidateReqVO {

    @Schema(description = "优惠码", requiredMode = Schema.RequiredMode.REQUIRED, example = "WELCOME2024")
    @NotBlank(message = "优惠码不能为空")
    private String couponCode;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @Schema(description = "订单总金额（分）", example = "10000")
    private Integer totalAmount;

}

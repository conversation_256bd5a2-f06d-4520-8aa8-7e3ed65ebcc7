package cn.iocoder.yudao.module.promotion.service.coupon.bo;

import cn.iocoder.yudao.module.promotion.dal.dataobject.coupon.CouponTemplateDO;
import lombok.Data;

/**
 * 优惠码验证响应 BO
 *
 * <AUTHOR>
 */
@Data
public class CouponCodeValidateRespBO {

    /**
     * 是否有效
     */
    private Boolean valid;
    /**
     * 无效原因
     */
    private String invalidReason;
    /**
     * 优惠券模板
     */
    private CouponTemplateDO template;
    /**
     * 优惠金额（分）
     */
    private Integer discountAmount;

    public static CouponCodeValidateRespBO success(CouponTemplateDO template, Integer discountAmount) {
        CouponCodeValidateRespBO respBO = new CouponCodeValidateRespBO();
        respBO.setValid(true);
        respBO.setTemplate(template);
        respBO.setDiscountAmount(discountAmount);
        return respBO;
    }

    public static CouponCodeValidateRespBO fail(String invalidReason) {
        CouponCodeValidateRespBO respBO = new CouponCodeValidateRespBO();
        respBO.setValid(false);
        respBO.setInvalidReason(invalidReason);
        return respBO;
    }

}

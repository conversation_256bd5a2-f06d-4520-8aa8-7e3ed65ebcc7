package cn.iocoder.yudao.module.promotion.dal.dataobject.coupon;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponBusinessTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 优惠码使用记录 DO
 *
 * <AUTHOR>
 */
@TableName(value = "promotion_coupon_usage_log", autoResultMap = true)
@KeySequence("promotion_coupon_usage_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class CouponUsageLogDO extends BaseDO {

    /**
     * 优惠劵编号
     */
    @TableId
    private Long id;
    /**
     * 优惠劵模板编号
     *
     * 关联 {@link CouponTemplateDO#getId()}
     */
    private Long templateId;
    /**
     * 使用的优惠码
     */
    private String couponCode;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 业务类型
     *
     * 枚举 {@link CouponBusinessTypeEnum}
     */
    private Integer businessType;
    /**
     * 业务ID（订单ID或代购包裹ID）
     */
    private Long businessId;
    /**
     * 优惠金额（分）
     */
    private Integer discountAmount;
    /**
     * 使用时间
     */
    private LocalDateTime useTime;

}

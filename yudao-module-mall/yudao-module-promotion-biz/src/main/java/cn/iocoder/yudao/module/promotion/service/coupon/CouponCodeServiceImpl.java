package cn.iocoder.yudao.module.promotion.service.coupon;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils;
import cn.iocoder.yudao.module.promotion.dal.dataobject.coupon.CouponTemplateDO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.coupon.CouponUsageLogDO;
import cn.iocoder.yudao.module.promotion.dal.mysql.coupon.CouponTemplateMapper;
import cn.iocoder.yudao.module.promotion.dal.mysql.coupon.CouponUsageLogMapper;
import cn.iocoder.yudao.module.promotion.enums.common.PromotionDiscountTypeEnum;
import cn.iocoder.yudao.module.promotion.enums.common.PromotionProductScopeEnum;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponCodeTypeEnum;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponTemplateValidityTypeEnum;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponUsageTypeEnum;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeUseReqBO;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeValidateReqBO;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeValidateRespBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 优惠码 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CouponCodeServiceImpl implements CouponCodeService {

    @Resource
    private CouponTemplateMapper couponTemplateMapper;
    @Resource
    private CouponUsageLogMapper couponUsageLogMapper;

    @Override
    public CouponCodeValidateRespBO validateCouponCode(CouponCodeValidateReqBO reqBO) {
        // 1. 根据优惠码查找模板
        CouponTemplateDO template = couponTemplateMapper.selectOne(CouponTemplateDO::getCouponCode, reqBO.getCouponCode());
        if (template == null) {
            return CouponCodeValidateRespBO.fail("优惠码不存在");
        }

        // 2. 验证模板状态
        if (!CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus())) {
            return CouponCodeValidateRespBO.fail("优惠码已停用");
        }

        // 3. 验证使用方式
        if (!CouponUsageTypeEnum.DIRECT_USE.getType().equals(template.getUsageType())) {
            return CouponCodeValidateRespBO.fail("该优惠券不支持直接使用优惠码");
        }

        // 4. 验证业务类型匹配
        if (!template.getBusinessType().equals(reqBO.getBusinessType())) {
            return CouponCodeValidateRespBO.fail("优惠码不适用于当前业务类型");
        }

        // 5. 验证有效期
        LocalDateTime now = LocalDateTime.now();
        if (CouponTemplateValidityTypeEnum.DATE.getType().equals(template.getValidityType())) {
            if (template.getValidStartTime() != null && now.isBefore(template.getValidStartTime())) {
                return CouponCodeValidateRespBO.fail("优惠码未到使用时间");
            }
            if (template.getValidEndTime() != null && now.isAfter(template.getValidEndTime())) {
                return CouponCodeValidateRespBO.fail("优惠码已过期");
            }
        }

        // 6. 验证使用次数限制
        if (template.getMaxUseCount() != null && template.getMaxUseCount() > 0) {
            Long usedCount = couponUsageLogMapper.countByCouponCode(reqBO.getCouponCode());
            if (usedCount >= template.getMaxUseCount()) {
                return CouponCodeValidateRespBO.fail("优惠码使用次数已达上限");
            }
        }

        // 7. 验证用户使用限制（每人限用次数）
        if (template.getTakeLimitCount() != null && template.getTakeLimitCount() > 0) {
            Long userUsedCount = couponUsageLogMapper.countByCouponCodeAndUserId(reqBO.getCouponCode(), reqBO.getUserId());
            if (userUsedCount >= template.getTakeLimitCount()) {
                return CouponCodeValidateRespBO.fail("您已达到该优惠码的使用次数上限");
            }
        }

        // 8. 验证订单金额
        if (template.getUsePrice() != null && template.getUsePrice() > 0) {
            if (reqBO.getTotalAmount() == null || reqBO.getTotalAmount() < template.getUsePrice()) {
                return CouponCodeValidateRespBO.fail(String.format("订单金额需满 %1$,.2f 元才能使用该优惠码", template.getUsePrice() / 100D));
            }
        }

        // 9. 验证商品范围
        if (!PromotionProductScopeEnum.ALL.getScope().equals(template.getProductScope())) {
            if (CollUtil.isEmpty(reqBO.getProductScopeValues())) {
                return CouponCodeValidateRespBO.fail("优惠码没有匹配的商品");
            }
            // 这里简化处理，实际应该根据具体的商品范围类型进行验证
            if (CollUtil.isEmpty(template.getProductScopeValues()) || 
                !CollUtil.containsAny(template.getProductScopeValues(), reqBO.getProductScopeValues())) {
                return CouponCodeValidateRespBO.fail("优惠码没有匹配的商品");
            }
        }

        // 10. 计算优惠金额
        Integer discountAmount = calculateDiscountAmount(template, reqBO.getTotalAmount());
        if (discountAmount <= 0) {
            return CouponCodeValidateRespBO.fail("优惠金额计算错误");
        }

        return CouponCodeValidateRespBO.success(template, discountAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void useCouponCode(CouponCodeUseReqBO reqBO) {
        // 1. 再次验证优惠码（防止并发问题）
        CouponCodeValidateReqBO validateReq = new CouponCodeValidateReqBO();
        validateReq.setCouponCode(reqBO.getCouponCode());
        validateReq.setUserId(reqBO.getUserId());
        validateReq.setBusinessType(reqBO.getBusinessType());
        
        CouponCodeValidateRespBO validateResp = validateCouponCode(validateReq);
        if (!validateResp.getValid()) {
            throw new RuntimeException("优惠码验证失败：" + validateResp.getInvalidReason());
        }

        // 2. 记录使用日志
        CouponUsageLogDO usageLog = new CouponUsageLogDO();
        usageLog.setTemplateId(validateResp.getTemplate().getId());
        usageLog.setCouponCode(reqBO.getCouponCode());
        usageLog.setUserId(reqBO.getUserId());
        usageLog.setBusinessType(reqBO.getBusinessType());
        usageLog.setBusinessId(reqBO.getBusinessId());
        usageLog.setDiscountAmount(reqBO.getDiscountAmount());
        usageLog.setUseTime(LocalDateTime.now());
        couponUsageLogMapper.insert(usageLog);

        // 3. 更新模板使用次数
        CouponTemplateDO updateTemplate = new CouponTemplateDO();
        updateTemplate.setId(validateResp.getTemplate().getId());
        updateTemplate.setUsedCount(validateResp.getTemplate().getUsedCount() + 1);
        couponTemplateMapper.updateById(updateTemplate);
    }

    @Override
    public String generateDynamicCouponCode(Long templateId) {
        // 动态码生成规则：模板ID + 时间戳 + 随机数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String random = RandomUtil.randomString(6).toUpperCase();
        return String.format("DC%d%s%s", templateId, timestamp.substring(timestamp.length() - 6), random);
    }

    /**
     * 计算优惠金额
     */
    private Integer calculateDiscountAmount(CouponTemplateDO template, Integer totalAmount) {
        if (totalAmount == null || totalAmount <= 0) {
            return 0;
        }

        Integer discountAmount = 0;
        if (PromotionDiscountTypeEnum.PRICE.getType().equals(template.getDiscountType())) {
            // 固定金额
            discountAmount = template.getDiscountPrice();
        } else if (PromotionDiscountTypeEnum.PERCENT.getType().equals(template.getDiscountType())) {
            // 百分比折扣
            discountAmount = totalAmount * template.getDiscountPercent() / 100;
            // 应用折扣上限
            if (template.getDiscountLimitPrice() != null && template.getDiscountLimitPrice() > 0) {
                discountAmount = Math.min(discountAmount, template.getDiscountLimitPrice());
            }
        }

        // 优惠金额不能超过订单金额
        return Math.min(discountAmount, totalAmount);
    }

}

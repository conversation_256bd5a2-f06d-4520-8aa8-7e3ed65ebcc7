package cn.iocoder.yudao.module.promotion.dal.mysql.coupon;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.promotion.dal.dataobject.coupon.CouponUsageLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 优惠码使用记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CouponUsageLogMapper extends BaseMapperX<CouponUsageLogDO> {

    /**
     * 根据优惠码和用户ID查询使用次数
     *
     * @param couponCode 优惠码
     * @param userId     用户ID
     * @return 使用次数
     */
    default Long countByCouponCodeAndUserId(String couponCode, Long userId) {
        return selectCount(CouponUsageLogDO::getCouponCode, couponCode,
                CouponUsageLogDO::getUserId, userId);
    }

    /**
     * 根据优惠码查询使用次数
     *
     * @param couponCode 优惠码
     * @return 使用次数
     */
    default Long countByCouponCode(String couponCode) {
        return selectCount(CouponUsageLogDO::getCouponCode, couponCode);
    }

}

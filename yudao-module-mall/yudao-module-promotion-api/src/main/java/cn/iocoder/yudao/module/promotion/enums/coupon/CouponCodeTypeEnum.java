package cn.iocoder.yudao.module.promotion.enums.coupon;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 优惠码类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CouponCodeTypeEnum implements IntArrayValuable {

    FIXED(1, "固定码"), // 如：WELCOME2024
    DYNAMIC(2, "动态码"); // 如：根据规则生成的唯一码

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CouponCodeTypeEnum::getType).toArray();

    /**
     * 值
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}

package cn.iocoder.yudao.module.promotion.enums.coupon;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 优惠券业务类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CouponBusinessTypeEnum implements IntArrayValuable {

    TRADE_ORDER(1, "商品订单"),
    AGENT_ORDER(2, "代购订单");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CouponBusinessTypeEnum::getType).toArray();

    /**
     * 值
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}

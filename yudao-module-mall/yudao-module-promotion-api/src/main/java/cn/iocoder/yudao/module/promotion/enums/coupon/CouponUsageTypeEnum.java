package cn.iocoder.yudao.module.promotion.enums.coupon;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 优惠券使用方式枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CouponUsageTypeEnum implements IntArrayValuable {

    TAKE_FIRST(1, "领取后使用"),
    DIRECT_USE(2, "直接使用优惠码");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CouponUsageTypeEnum::getType).toArray();

    /**
     * 值
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}

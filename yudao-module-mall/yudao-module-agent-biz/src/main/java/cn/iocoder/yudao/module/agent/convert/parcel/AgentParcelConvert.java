package cn.iocoder.yudao.module.agent.convert.parcel;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.agent.api.stock.dto.AgentStockUpdateReqDTO;
import cn.iocoder.yudao.module.agent.controller.admin.base.member.user.MemberUserRespVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelDetailRespVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelPageItemRespVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelRemarkReqVO;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelUpdateAddressReqVO;
import cn.iocoder.yudao.module.agent.controller.app.base.property.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.agent.controller.app.parcel.vo.*;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelItemDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcellog.AgentParcelLogDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.stock.AgentStockDO;
import cn.iocoder.yudao.module.agent.framework.delivery.core.client.dto.ExpressTrackRespDTO;
import cn.iocoder.yudao.module.agent.framework.parcel.config.AgentParcelProperties;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateReqBO;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateRespBO;
import cn.iocoder.yudao.module.agent.service.serve.bo.AgentServerBO;
import cn.iocoder.yudao.module.member.api.address.dto.MemberAddressRespDTO;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderCreateReqDTO;
import cn.iocoder.yudao.module.trade.enums.order.TradeOrderItemAfterSaleStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMultiMap;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.addTime;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购包裹转换
 * @author: DingXiao
 * @create: 2025-04-30 17:38
 **/

@Mapper
public interface AgentParcelConvert {

    AgentParcelConvert INSTANCE = Mappers.getMapper(AgentParcelConvert.class);


    default AgentPriceCalculateReqBO convert(Long userId, AppAgentParcelSettlementReqVO settlementReqVO, List<AgentStockDO> stockDOList) {
        AgentPriceCalculateReqBO reqBO = new AgentPriceCalculateReqBO()
                .setUserId(userId)
                .setItems(settlementReqVO.getItems() != null ? new ArrayList<>(settlementReqVO.getItems().size()) : new ArrayList<>())
                .setCouponId(settlementReqVO.getCouponId())
                .setCouponCode(settlementReqVO.getCouponCode())
                .setPointStatus(settlementReqVO.getPointStatus())
                .setAddressId(settlementReqVO.getAddressId())
                .setInsuranceServices(settlementReqVO.getInsuranceServices())
                .setFreeServices(settlementReqVO.getFreeServices())
                .setChargeServices(settlementReqVO.getChargeServices())
                .setTransportPlanId(settlementReqVO.getTransportPlanId())
                .setTransportPlanFeeId(settlementReqVO.getTransportPlanFeeId());

        Map<Long, AgentStockDO> stockMap = convertMap(stockDOList, AgentStockDO::getId);

        if (settlementReqVO.getItems() == null) {
            return reqBO;
        }
        for (AppAgentParcelSettlementReqVO.Item item : settlementReqVO.getItems()) {
            reqBO.getItems().add(new AgentPriceCalculateReqBO.Item()
                    .setStockId(item.getStockId())
                    .setSkuId(stockMap.get(item.getStockId()).getSkuId())//使用库存的信息
                    .setSpuId(stockMap.get(item.getStockId()).getSpuId())
                    .setSpuName(stockMap.get(item.getStockId()).getSpuName())
                    .setProperties( BeanUtil.copyToList(stockMap.get(item.getStockId()).getProperties(),  AppProductPropertyValueDetailRespVO.class))
                    .setCount(item.getCount()>stockMap.get(item.getStockId()).getCount() ? stockMap.get(item.getStockId()).getCount() : item.getCount())  //修改为使用前端数量(如果前端数量大于库存数量则使用库存数量)  使用库存的数量? 是否允许用户修改数量呢 分批发货 todo stockMap.get(item.getStockId()).getCount()
                    .setStockCount(stockMap.get(item.getStockId()).getCount()) //库存数量
                    .setWeight(stockMap.get(item.getStockId()).getWeight()) //使用库存的重量
                    .setVolume(stockMap.get(item.getStockId()).getVolume()) //使用库存的体积
                    .setPrePackageWeight(stockMap.get(item.getStockId()).getPrePackageWeight()) //使用库存的预计包装重量
                    .setLength(stockMap.get(item.getStockId()).getLength())
                    .setWidth(stockMap.get(item.getStockId()).getWidth())
                    .setHeight(stockMap.get(item.getStockId()).getHeight())
                    .setPicUrl(stockMap.get(item.getStockId()).getPicUrl()));

        }
        return reqBO;
    }

    default AppAgentParcelSettlementRespVO convert(AgentPriceCalculateRespBO calculate, MemberAddressRespDTO address){
        AppAgentParcelSettlementRespVO respVO = convert0(calculate, address);
        if(address != null){
            //国际化地址格式
            respVO.getAddress().setAreaName(AreaUtils.formatInternational(address.getAreaId()));
        }
        return respVO;
    }

    AppAgentParcelSettlementRespVO convert0(AgentPriceCalculateRespBO calculate, MemberAddressRespDTO address);


    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(source = "userId", target = "userId"),
            @Mapping(source = "createReqVO.couponId", target = "couponId"),
            @Mapping(source = "calculateRespBO.couponCode", target = "couponCode"),
            @Mapping(source = "createReqVO.transportPlanId", target = "transportPlanId"),
            @Mapping(source = "createReqVO.transportPlanFeeId", target = "transportPlanFeeId"),
            @Mapping(target = "remark", ignore = true),
            @Mapping(source = "createReqVO.userRemark", target = "userRemark"),
            @Mapping(source = "calculateRespBO.price.totalPrice", target = "totalPrice"),
            @Mapping(source = "calculateRespBO.price.discountPrice", target = "discountPrice"),
            @Mapping(source = "calculateRespBO.price.deliveryPrice", target = "deliveryPrice"),
            @Mapping(source = "calculateRespBO.price.couponPrice", target = "couponPrice"),
            @Mapping(source = "calculateRespBO.price.pointPrice", target = "pointPrice"),
            @Mapping(source = "calculateRespBO.price.vipPrice", target = "vipPrice"),
            @Mapping(source = "calculateRespBO.price.payPrice", target = "payPrice"),
            @Mapping(source = "calculateRespBO.price.insurancePrice", target = "insurancePrice"),
            @Mapping(source = "calculateRespBO.price.servicePrice", target = "servicePrice"),
            @Mapping(source = "calculateRespBO.price.platformPrice", target = "platformPrice"),
            @Mapping(source = "createReqVO.insuranceServices", target = "insuranceServices"),
            @Mapping(source = "createReqVO.freeServices", target = "freeServices"),
            @Mapping(source = "createReqVO.chargeServices", target = "chargeServices")
    })
    AgentParcelDO convert(Long userId, AppAgentParcelCreateReqVO createReqVO, AgentPriceCalculateRespBO calculateRespBO);



    default List<AgentParcelItemDO> convertList(AgentParcelDO parcelDO, AgentPriceCalculateRespBO calculateRespBO){
        return CollectionUtils.convertList(calculateRespBO.getItems(), item -> {
            AgentParcelItemDO orderItem = convert(item);
            orderItem.setParcelId(parcelDO.getId());
            orderItem.setUserId(parcelDO.getUserId());
            orderItem.setAfterSaleStatus(TradeOrderItemAfterSaleStatusEnum.NONE.getStatus());
            orderItem.setCommentStatus(false);
            return orderItem;
        });
    }

    AgentParcelItemDO convert(AgentPriceCalculateRespBO.Item item);

    default AgentStockUpdateReqDTO convert(List<AgentParcelItemDO> list) {
        List<AgentStockUpdateReqDTO.Item> items = CollectionUtils.convertList( list, item -> new AgentStockUpdateReqDTO.Item().setId(item.getStockId()).setIncrCount(item.getCount()));
        return new AgentStockUpdateReqDTO(items);
    }

    default AgentStockUpdateReqDTO convertNegative(List<AgentParcelItemDO> list){
        List<AgentStockUpdateReqDTO.Item> items = CollectionUtils.convertList( list, item -> new AgentStockUpdateReqDTO.Item().setId(item.getStockId()).setIncrCount(-item.getCount()));
        return new AgentStockUpdateReqDTO(items);
    }

    default PayOrderCreateReqDTO convert(AgentParcelDO order, List<AgentParcelItemDO> orderItems,
                                         AgentParcelProperties agentParcelProperties, String currency) {
        PayOrderCreateReqDTO createReqDTO = new PayOrderCreateReqDTO()
                .setAppKey(agentParcelProperties.getPayAppKey()).setUserIp(order.getUserIp());
        // 商户相关字段
        createReqDTO.setMerchantOrderId(String.valueOf(order.getId()));
        String subject = orderItems.get(0).getSpuName();
        subject = StrUtils.maxLength(subject, PayOrderCreateReqDTO.SUBJECT_MAX_LENGTH); // 避免超过 32 位
        createReqDTO.setSubject(subject);
        createReqDTO.setBody(subject); // TODO 临时写死
        // 订单相关字段
        createReqDTO.setPrice(order.getPayPrice())
                .setExpireTime(addTime(agentParcelProperties.getPayExpireTime()))
                .setOrderPrice(order.getPayPrice())
                .setCurrency(currency);
        return createReqDTO;
    }

    List<AppAgentParcelRespVO.Item> convertList(List<AgentParcelItemDO> agentParcelItemDOS);

    @Mapping(target = "insuranceServices", ignore = true)
    @Mapping(target = "freeServices", ignore = true)
    @Mapping(target = "chargeServices", ignore = true)
    AppAgentParcelRespVO convert(AgentParcelDO parcel, List<AgentParcelItemDO> items);


    @Mapping(target = "insuranceServices", ignore = true)
    @Mapping(target = "freeServices", ignore = true)
    @Mapping(target = "chargeServices", ignore = true)
    AppAgentParcelRespVO convert(AgentParcelDO agentParcelDO);

    default PageResult<AppAgentParcelRespVO> convertPage(PageResult<AgentParcelDO> pageResult) {
        List<AppAgentParcelRespVO> vos = pageResult.getList().stream()
                .map(this::convert)
                .collect(Collectors.toList());
        return new PageResult<>(vos, pageResult.getTotal());
    }


    default PageResult<AgentParcelPageItemRespVO> convertPage(PageResult<AgentParcelDO> pageResult, List<AgentParcelItemDO> parcelItems,
                                                              Map<Long, MemberUserRespDTO> memberUserMap, Map<Long, LogisticsProductDO> planMap,
                                                              Map<Long, AgentServerBO> serverMap ) {

        Map<Long, List<AgentParcelItemDO>> orderItemMap = convertMultiMap(parcelItems, AgentParcelItemDO::getParcelId);
        // 转化 List
        List<AgentParcelPageItemRespVO> orderVOs = CollectionUtils.convertList(pageResult.getList(), order -> {
            List<AgentParcelItemDO> xOrderItems = orderItemMap.get(order.getId());
            AgentParcelPageItemRespVO orderVO = convert1(order, xOrderItems);
            // 处理收货地址
            orderVO.setReceiverAreaName(AreaUtils.format(order.getReceiverAreaId()));
            // 增加用户信息
            orderVO.setUser(convertUser(memberUserMap.get(orderVO.getUserId())));
            // 增加推广人信息
            orderVO.setBrokerageUser(convertUser(memberUserMap.get(orderVO.getBrokerageUserId())));
            //增加物流货运信息
            orderVO.setTransportCompanyName(planMap.get(orderVO.getTransportPlanId()).getCompanyName());
            orderVO.setTransportPlanName(planMap.get(orderVO.getTransportPlanId()).getNameZh());
            //增加服务项信息
            orderVO.setInsuranceServerList(convertServerList(orderVO.getInsuranceServices(), serverMap));
            orderVO.setFreeServerList(convertServerList(orderVO.getFreeServices(), serverMap));
            orderVO.setChargeServerList(convertServerList(orderVO.getChargeServices(), serverMap));
            return orderVO;
        });
        return new PageResult<>(orderVOs, pageResult.getTotal());

    }

    default List<AgentParcelPageItemRespVO.Server> convertServerList(List<Long> services, Map<Long, AgentServerBO> serverMap){
        return services.stream().map(serverMap::get).filter(Objects::nonNull).map(server -> {
            AgentParcelPageItemRespVO.Server serverVO = new AgentParcelPageItemRespVO.Server();
            BeanUtils.copyProperties(server, serverVO);
            return serverVO;
        }).collect(Collectors.toList());
    }


    AgentParcelPageItemRespVO convert1(AgentParcelDO parcel, List<AgentParcelItemDO> items);

    MemberUserRespVO convertUser(MemberUserRespDTO memberUserRespDTO);

    default AgentParcelDetailRespVO convert(AgentParcelDO parcel, List<AgentParcelItemDO> parcelItems,
                                            List<AgentParcelLogDO> orderLogs, MemberUserRespDTO user, 
                                            MemberUserRespDTO brokerageUser,LogisticsProductDO transportPlan,
                                            Map<Long, AgentServerBO> serverMap){
        AgentParcelDetailRespVO respVO = convert2(parcel,  parcelItems);
        // 处理收货地址
        respVO.setReceiverAreaName(AreaUtils.format(parcel.getReceiverAreaId()));
        // 处理货运方案
        respVO.setTransportCompanyName(transportPlan.getCompanyName());
        respVO.setTransportPlanName(transportPlan.getNameZh());
        //处理服务
        respVO.setInsuranceServerList(convertServerList1(respVO.getInsuranceServices(), serverMap));
        respVO.setFreeServerList(convertServerList1(respVO.getFreeServices(), serverMap));
        respVO.setChargeServerList(convertServerList1(respVO.getChargeServices(), serverMap));
        // 处理用户信息
        respVO.setUser(convert(user));
        respVO.setBrokerageUser(convert(brokerageUser));
        // 处理日志
        respVO.setLogs(convertList03(orderLogs));
        return respVO;
    }
    default List<AgentParcelDetailRespVO.Server> convertServerList1(List<Long> services, Map<Long, AgentServerBO> serverMap){
        return services.stream().map(serverMap::get).filter(Objects::nonNull).map(server -> {
            AgentParcelDetailRespVO.Server serverVO = new AgentParcelDetailRespVO.Server();
            BeanUtils.copyProperties(server, serverVO);
            return serverVO;
        }).collect(Collectors.toList());
    }

    List<AgentParcelDetailRespVO.ParcelLog> convertList03(List<AgentParcelLogDO> orderLogs);

    MemberUserRespVO convert(MemberUserRespDTO user);

    AgentParcelDetailRespVO convert2(AgentParcelDO parcel, List<AgentParcelItemDO> items);

    AgentParcelDO convert(AgentParcelRemarkReqVO reqVO);

    AgentParcelDO convert(AgentParcelUpdateAddressReqVO reqVO);

    List<AppAgentParcelTrackRespDTO> convertList02(List<ExpressTrackRespDTO> list);
}

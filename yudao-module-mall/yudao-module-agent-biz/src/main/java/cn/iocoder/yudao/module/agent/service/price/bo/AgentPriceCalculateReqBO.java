package cn.iocoder.yudao.module.agent.service.price.bo;

import cn.iocoder.yudao.module.agent.controller.app.base.property.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.trade.enums.delivery.DeliveryTypeEnum;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 货运单价格计算 Request BO
 * @author: DingXiao
 * @create: 2025-04-20 19:42
 **/
@Data
public class AgentPriceCalculateReqBO {
    /**
     * 用户编号
     *
     * 对应 MemberUserDO 的 id 编号
     */
    private Long userId;

    /**
     * 优惠劵编号
     *
     * 对应 CouponDO 的 id 编号
     */
    private Long couponId;

    /**
     * 优惠码
     */
    private String couponCode;

    /**
     * 是否使用积分
     */
    @NotNull(message = "是否使用积分不能为空")
    private Boolean pointStatus;

    /**
     * 配送方式
     *
     * 枚举 {@link DeliveryTypeEnum}
     */
    private Integer deliveryType;
    /**
     * 收货地址编号
     *
     * 对应 MemberAddressDO 的 id 编号
     */
    private Long addressId;
    /**
     * 自提门店编号
     *
     * 对应 PickUpStoreDO 的 id 编号
     */
    private Long pickUpStoreId;

    /**
     * 商品 SKU 数组
     */
    @NotNull(message = "商品数组不能为空")
    private List<Item> items;

    // ========== 秒杀活动相关字段 ==========
    /**
     * 秒杀活动编号
     */
    private Long seckillActivityId;

    // ========== 拼团活动相关字段 ==========
    /**
     * 拼团活动编号
     */
    private Long combinationActivityId;

    /**
     * 拼团团长编号
     */
    private Long combinationHeadId;

    // ========== 砍价活动相关字段 ==========
    /**
     * 砍价记录编号
     */
    private Long bargainRecordId;

    // ========== 积分商城活动相关字段 ==========
    /**
     * 积分商城活动编号
     */
    private Long pointActivityId;

    //代购相关
    //物流方案编号
    private Long transportPlanId;
    //物流方案价格编号
    private Long transportPlanFeeId;

    //保险服务选项IDs
    private List<Long> insuranceServices;

    //免费服务选项IDs
    private List<Long> freeServices;

    //增值服务选项IDs
    private List<Long> chargeServices;

    /**
     * 商品 SKU
     */
    @Data
    @Valid
    public static class Item {

        /**
         * 库存项的编号
         */
        private Long stockId;

        /**
         * 分类编号
         */
        private Long categoryId;


        /**
         * SKU 编号
         */
        private Long skuId;

        /**
         * SPU 编号
         */
        private Long spuId;

        /**
         * 商品 SPU 名称
         */
        private String spuName;

        /**
         * SKU 数量
         */
        private Integer count;
        /**
         * 库存 数量
         */
        private Integer stockCount;

        /**
         * 商品属性数组，
         */
        private List<AppProductPropertyValueDetailRespVO> properties;


        /**
         * 单价，单位分，代购商品用户修改的单价 （代购增加字段 价格可能被用户修改）
         */
        private Integer price;

        /**
         * 重量，单位克
         */
        private Integer weight;

        /**
         * 体积，单位立方厘米
         */
        private BigDecimal volume;

        /**
         * 长
         */
        private BigDecimal length;
        /**
         * 宽
         */
        private BigDecimal width;
        /**
         * 高
         */
        private BigDecimal height;

        /**
         * 预包装重量，单位克
         */
        private Integer prePackageWeight;

        /**
         * 商品图片
         */
        private String picUrl;

    }


}

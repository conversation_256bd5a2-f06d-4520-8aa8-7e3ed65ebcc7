package cn.iocoder.yudao.module.agent.dal.dataobject.parcel;

import cn.iocoder.yudao.framework.mybatis.core.type.LongListTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代购包裹 DO
 *
 * <AUTHOR>
 */
@TableName(value = "agent_parcel", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentParcelDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 流水号
     */
    private String no;
    /**
     * 来源终端
     *
     * 枚举 {@link TODO terminal 对应的类}
     */
    private Integer terminal;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 用户 IP
     */
    private String userIp;
    /**
     * 用户备注
     */
    private String userRemark;
    /**
     * 包裹状态
     *
     * 枚举 {@link TODO agent_parcel_status 对应的类}
     */
    private Integer status;
    /**
     * 包裹商品数量
     */
    private Integer productCount;
    /**
     * 取消类型
     */
    private Integer cancelType;
    /**
     * 平台备注
     */
    private String remark;
    /**
     * 是否评价
     */
    private Boolean commentStatus;
    /**
     * 推广人编号
     */
    private Long brokerageUserId;
    /**
     * 支付订单编号
     */
    private Long payOrderId;
    /**
     * 支付订单编号集合
     */
    private String payOrderIds;
    /**
     * 是否支付
     */
    private Boolean payStatus;
    /**
     * 订单支付时间
     */
    private LocalDateTime payTime;
    /**
     * 支付成功的支付渠道
     */
    private String payChannelCode;
    /**
     * 包裹完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 包裹取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * 订单原价
     */
    private Integer totalPrice;
    /**
     * 订单优惠
     */
    private Integer discountPrice;
    /**
     * 运费金额
     */
    private Integer deliveryPrice;
    /**
     * 保险金额
     */
    private Integer insurancePrice;
    /**
     * 服务金额
     */
    private Integer servicePrice;

    /**
     * 平台费，单位：分
     */
    private Integer platformPrice;
    /**
     * 订单调价
     */
    private Integer adjustPrice;
    /**
     * 应付金额
     */
    private Integer payPrice;
    /**
     * 商品重量 单位：克
     */
    private Integer weight;
    /**
     * 商品体积
     */
    private BigDecimal volume;
    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal width;
    /**
     * 高
     */
    private BigDecimal height;
    /**
     * 包装重量 单位：克
     */
    private Integer packingWeight;
    /**
     * 配送类型
     */
    private Integer deliveryType;
    /**
     * 物流方案编号
     */
    private Long transportPlanId;
    /**
     * 物流方案费用编号
     */
    private Long transportPlanFeeId;
    /**
     * 物流公司编号
     */
    private Long transportCompanyId;
    /**
     * 物流单号
     */
    private String transportNo;
    /**
     * 物流跟踪
     */
    private String transportTrack;
    /**
     * 海关申报内容
     */
    private String declareContent;
    /**
     * 海关申报价值
     */
    private Integer declareValue;
    /**
     * 清关代码
     */
    private String clearanceCode;
    /**
     * 保险服务数组
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> insuranceServices;
    /**
     * 免费服务数组
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> freeServices;
    /**
     * 收费服务数组
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> chargeServices;
    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 收货时间
     */
    private LocalDateTime receiveTime;
    /**
     * 收件人名称
     */
    private String receiverName;
    /**
     * 收件人手机
     */
    private String receiverMobile;
    /**
     * 收件人地区编号
     */
    private Integer receiverAreaId;
    /**
     * 收件人详细地址
     */
    private String receiverDetailAddress;
    /**
     * 收件人手机
     */
    private String receiverPhoneCode;
    /**
     * 邮编
     */
    private String receiverPostCode;
    /**
     * 国家编码
     */
    private String receiverCountryCode;
    /**
     * 售后状态
     */
    private Integer refundStatus;
    /**
     * 退款金额
     */
    private Integer refundPrice;
    /**
     * 优惠劵编号
     */
    private Long couponId;
    /**
     * 优惠码
     */
    private String couponCode;
    /**
     * 优惠劵减免金额
     */
    private Integer couponPrice;
    /**
     * 使用的积分
     */
    private Integer usePoint;
    /**
     * 积分抵扣的金额
     */
    private Integer pointPrice;
    /**
     * 赠送的积分
     */
    private Integer givePoint;
    /**
     * 退还的使用的积分
     */
    private Integer refundPoint;
    /**
     * VIP 减免金额
     */
    private Integer vipPrice;
    /**
     * 赠送的优惠劵
     *
     * key: 优惠劵模版编号
     * value：对应的优惠券数量
     *
     * 目的：用于订单支付后赠送优惠券
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<Long, Integer> giveCouponTemplateCounts;
    /**
     * 赠送的优惠劵编号
     *
     * 目的：用于后续取消或者售后订单时，需要扣减赠送
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> giveCouponIds;

}